<template>
  <div class="cardContent">
    <template v-for="(item, index) in cardList" :key="index">
      <div class="contain" @click="goDeatil(item)">
        <div class="cardBox">
          <div
            class="topCot"
            :style="{ backgroundImage: `url('${item.image}')` }"
          >
            <div class="time">{{ item.createTime.slice(0, 10) }}</div>
          </div>
          <div class="botCot">
            <div class="flex just-sb align-center">
              <div class="text">{{ item.name }}</div>
              <img
                src="@/assets/images/home/<USER>"
                width="22px"
                height="12px"
                alt=""
              />
            </div>
            <div class="word">
              {{ item.introduce }}
            </div>
          </div>
        </div>
      </div>
    </template>
  </div>

  <div class="layPage" v-if="cardList && cardList.length > 0">
    <a-pagination
      v-model:pageSize="pageItemSize"
      v-model:current="currentPage"
      :pageSizeOptions="pageSizeOptions"
      show-quick-jumper
      show-size-changer
      :total="totalItemCount"
      @change="pageChange"
      @showSizeChange="sizeChange"
      class="mypage"
    />
  </div>

  <div v-if="emptyShow" class="emptyPhoto">
    <img src="@/assets/images/home/<USER>" alt=""/>
  </div>
  <div class="loading" v-show="loadingShow">
    <a-spin />
  </div>
</template>

<script>
import { defineComponent, reactive, nextTick, toRefs } from "vue";
import { getList } from "@/api/prefecture/home";
import { useRouter } from "vue-router";
import { currentTab } from "@/store";

export default defineComponent({
  name: "prefecture",
  setup() {
    const Router = useRouter();
    const getCurTab = currentTab();
    const embedUrl = import.meta.env.VITE_EMBED_URL;
    const data = reactive({
      name: "",
      cardList: [],
      pageSizeOptions: ["9", "10", "20", "50"],
      totalItemCount: 0,
      currentPage: 1,
      pageItemSize: 9,
      loadingShow: false,
      emptyShow: false,
    });
    const getListData = () => {
      let params = {
        name: data.name,
        pageNo: data.currentPage,
        pageSize: data.pageItemSize,
      };
      data.loadingShow = true;
      getList(params)
        .then((res) => {
          data.loadingShow = false;
          data.cardList = res.data.records;
          data.totalItemCount = res.data.total;
          if (data.totalItemCount == 0) {
            data.emptyShow = true;
          }
        })
        .catch(() => {
          data.loadingShow = false;
        });
    };
    getListData();
    const pageChange = (page) => {
      data.currentPage = page;
      getListData();
    };
    const sizeChange = (_current, size) => {
      data.pageItemSize = size;
      getListData();
    };
    const seekContent = () => {
      data.currentPage = 1;
      getListData();
    };
    const goDeatil = (item) => {
      if(item.id == 31) {
        const userInfo = JSON.parse(localStorage.getItem("userInfo"));
        getCurTab.linkUrl = `https://ict.futuresoft.com.cn/login?phone=${userInfo.phone}`;
        nextTick(() => {
          Router.push({
            name: "thirdView",
          });
        });
        return
      }
      if (item.zoneType == 1) {
        Router.push({
          name: "lowLevel",
          query: {
            zoneId: item.id,
          },
        });
        return;
      }
      if (item.applicationMarket == 3) {
        // 测试：http://36.138.46.45:8081
        // 生产：https://home.jsisi.cn:8099
        getCurTab.linkUrl = `${embedUrl}/hdict/index?token=${localStorage.getItem("token")}`;
        nextTick(() => {
          Router.push({
            name: "thirdView",
          });
        });
        return;
      }
      Router.push({
        name: "proLevel",
        query: {
          appMarket: item.applicationMarket,
          zoneId: item.id,
        },
      });
    };
    return {
      ...toRefs(data),
      seekContent,
      pageChange,
      getCurTab,
      sizeChange,
      goDeatil,
    };
  },
});
</script>

<style lang="scss" scoped>
.emptyPhoto {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);

  img {
    width: 240px;
    height: 248px;
  }
}
.loading {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}
.topImage {
  width: 1200px;
  margin: 0 auto;
  height: 420px;
  background-image: url("@/assets/images/prefecture/areaBac.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .topImage_content {
    padding: 63px 0 16px 0;

    .topImage_title {
      font-weight: bold;
      font-size: 56px;
      color: #0c70eb;
      text-align: center;
    }

    .topImage_details {
      width: 676px;
      font-size: 16px;
      color: #2b3f66;
      line-height: 32px;
      margin: 16px auto 0;
    }
  }
  .searchInfo {
    margin: 0 auto;
    width: 880px;
    padding: 24px;

    .vocationPull {
      background: #ffffff;
      display: flex;
      align-items: center;
      border-top-left-radius: 5px;
      border-bottom-left-radius: 5px;
      border-top-right-radius: 5px;
      border-bottom-right-radius: 5px;
    }

    .inputClass {
      border: none;
      width: 100%;
      height: 100%;
      box-shadow: none !important;
    }

    .line {
      width: 1px;
      height: 26px;
      background: #eff0f4;
    }

    .lines {
      width: 1px;
      height: 56px;
      background: #eff0f4;
    }

    .seekInfo {
      background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
      font-weight: bold;
      font-size: 20px;
      color: #ffffff;
      display: flex;
      align-items: center;
      width: 160px;
      height: 56px;
      cursor: pointer;
      border-top-right-radius: 5px;
      border-bottom-right-radius: 5px;

      img {
        width: 24px;
        height: 24px;
        margin-left: 20px;
        margin-right: 8px;
      }
    }

    .stage {
      margin-left: 44px;
      margin-top: 16px;
    }
  }
}
.layPage {
  width: 100%;
  text-align: center;
  margin: 15px 0;
}
.cardContent {
  display: flex;
  align-items: center;
  flex-wrap: wrap;
  width: 1200px;
  margin: 0 auto;
  margin-top: 30px;
  gap: 32px;

  .contain {
    width: 378px;
    cursor: pointer;
    box-sizing: border-box;

    .cardBox {
      height: 360px;
      border-radius: 16px 16px 16px 16px;
      border: 1px solid #dae2f5;
      background-color: #ffffff;

      .topCot {
        height: 245px;
        background-size: 100% 100%;
        background-repeat: no-repeat;
        border-top-left-radius: 16px;
        border-top-right-radius: 16px;
        padding: 20px 0 0 24px;
        .time {
          background: rgba(0, 0, 0, 0.2);
          border-radius: 8px;
          font-size: 14px;
          color: #ffffff;
          padding: 6px 12px;
          width: fit-content;
        }
      }
      .botCot {
        margin: 0 32px;
        .text {
          font-weight: bold;
          font-size: 20px;
          color: rgba(0, 0, 0, 0.85);
          margin: 12px 0;
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 1;
          overflow: hidden;
          text-overflow: ellipsis;
        }
        .word {
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
          display: -webkit-box;
          -webkit-box-orient: vertical;
          -webkit-line-clamp: 2;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
    }
  }
}
</style>

<style lang="scss">
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}
</style>