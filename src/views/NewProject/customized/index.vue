<template>
  <div class="body">
    <div class="loading-overlay" v-if="loadShow">
      <a-spin :spinning="loadShow" tip="附件加载中"></a-spin>
    </div>
    <div class="loading-overlay" v-if="loadShow1">
      <a-spin :spinning="loadShow1"></a-spin>
    </div>
    <!--<div v-if="progressPercent > -1">
      <div style="width: 50%; height: 20px">
        <a-progress :percent="progressPercent" status="active" />
        <div style="text-align: center; color: #1890ff; font-size: 14px">
        	方案生成中
        </div>
      </div>
    </div>-->
    <div class="loading-overlay" v-if="progressPercent > -1">
      <div class="loading-super">
        <superLoading :endFunction="endFunction" />
      </div>
    </div>

    <div class="combine">
      <div class="listContent scrollable-content" ref="container">
        <div class="flex just-sb line">
          <div class="title">
            封面
            <span class="sub-count"></span>
          </div>
          <div>
            <!-- zdl ↓ -->
            <!-- <a-button :class="{ fontDisabled: isDisabled }" @click="getExplain()">
              <span>生成讲解</span>
            </a-button> -->
            <!-- zdl ↑ -->
            <a-button :class="{ fontDisabled: isDisabled }" @click="showDetail">
              <span>一键调度</span>
            </a-button>
            <a-button :class="{ fontDisabled: isDisabled }" @click="view('选择预览文件')" :disabled="isDisabled">
              <span>生成方案</span>
            </a-button>
          </div>
        </div>
        <a-form :model="formData" class="formRef1" ref="formRef1" id="1">
          <a-form-item label="封面标题" name="title" :rules="[
            { required: true, message: '请输入封面标题', trigger: 'blur' },
          ]">
            <a-input class="no-border" v-model:value="formData.title" @blur="setTitle('title')"
              placeholder="请输入封面标题"></a-input>
          </a-form-item>
        </a-form>

      </div>
      <div class="listContent scrollable-content">
        <div class="flex just-sb line">
          <div class="title">
            方案概述（政策、需求等）
            <span class="sub-count">
              ({{
                AllcombineList.combineList
                  ? AllcombineList.combineList.length
                  : 0
              }})
            </span>
          </div>
        </div>
        <div class="tabContent" v-if="
          AllcombineList.combineList && AllcombineList.combineList.length > 0
        ">
          <div class="cardContent">
            <div class="card_total flex-1">
              <draggable :list="AllcombineList.combineList" animation="300" :disabled="true">
                <template v-for="(element, index) in AllcombineList.combineList" :key="index" #item="{ element }">
                  <div :class="['card_content']">
                    <img v-if="element.new" style="width: 48px; height: 20px" class="newImg" alt=""
                      src="@/assets/images/combine/new.png" />
                    <div style="display: flex; padding: 24px">
                      <div class="imgbox">
                        <img style="width: 28px; height: 28px" class="moduleImg" alt=""
                          src="@/assets/images/combine/modelBefore.png" />
                        <a-image :width="168" :height="105" :preview="false" v-if="element.picture"
                          :src="`${element.picture}`" style="width: 100%; height: 100%" />
                        <img src="@/assets/images/home/<USER>" style="width: 168px; height: 105px" alt="" v-else />
                      </div>
                      <div class="card_center">
                        <div class="card_text">
                          <div class="card_tag">
                            <div class="card_title">
                              {{ element.name }}
                            </div>
                            <div>
                              <span class="cardTag" style="
                                  background-color: #d7e6ff;
                                  color: #2e7fff;
                                ">{{ element.category }}</span>
                              <span class="author">联系人：{{ element.contact }}</span>
                              <span>时间：{{
                                getYearMonthDay(element.createTime)
                              }}</span>
                            </div>
                          </div>
                          <div class="flex">
                            <div class="viewStyle" v-if="isDraDisabled(element, 0)"
                              @click.stop="viewFile(element.fileList[0])">
                              <img class="add-icon" src=" @/assets/images/newProject/view.png" alt="" />
                              <span class="add"> &nbsp;预览</span>
                            </div>
                          </div>
                        </div>
                        <div class="card_des">
                          {{ element.introduce }}
                        </div>
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          " v-if="element.functionNames">
                          <div style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            ">
                            <span v-for="(
tagItem, tagIndex
                              ) in element.functionNames" :key="tagIndex" class="tag">
                              <span v-if="
                                tagIndex <
                                countTagIndex(element.functionNames)
                              ">
                                {{ tagItem }}
                              </span>
                            </span>
                            <span v-if="
                              sumTagLength(element.functionNames) &&
                              countTagIndex(element.functionNames) !=
                              element.functionNames.length
                            " class="tag" :title="element.functionNames
                              .slice(
                                countTagIndex(element.functionNames),
                                element.functionNames.length
                              )
                              .join(' , ')
                              ">
                              <span style="position: relative">
                                <div style="position: absolute; left: 4px">
                                  ...
                                </div>
                              </span>
                            </span>
                          </div>
                        </div>
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          ">
                          <div style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            ">
                            <span v-for="(fileItem, index) in element.fileList" :key="index" style="color: #0c70eb"
                              @click.stop="viewFile(fileItem)">
                              <!--
                                  <PaperClipOutlined />
                                  <span style="margin-left: 4px">{{ fileItem.fileName }}</span>
                                  -->
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
          </div>
        </div>
      </div>

      <div class="listContent scrollable-content">
        <div class="flex just-sb line">
          <div class="title">
            应用场景
            <span class="sub-count">({{
              AllcombineList.combineList5
                ? AllcombineList.combineList5.length
                : 0
            }})</span>
          </div>
        </div>
        <div class="tabContent" v-if="
          AllcombineList.combineList5 &&
          AllcombineList.combineList5.length > 0
        ">
          <div class="cardContent">
            <div class="card_total flex-1">
              <draggable :list="AllcombineList.combineList5" animation="300" filter=".forbid" :move="onMove">
                <template v-for="(element, index) in AllcombineList.combineList5" :key="index" #item="{ element }">
                  <div :class="isDraDisabled(element, 0)
                    ? ['card_content']
                    : ['card_content', 'forbid']
                    " @click="goDetail(element)">
                    <img v-if="element.new" style="width: 48px; height: 20px" class="newImg"
                      src="@/assets/images/combine/new.png" alt="" />
                    <div style="display: flex; padding: 24px">
                      <div class="imgbox">
                        <img style="width: 28px; height: 28px" class="moduleImg" alt=""
                          src="@/assets/images/combine/modelBefore.png" />
                        <a-image :width="168" :height="105" :preview="false" v-if="element.picture"
                          :src="`${element.picture}`" style="width: 100%; height: 100%" />
                        <img src="@/assets/images/home/<USER>" style="width: 168px; height: 105px" alt="" v-else />
                      </div>
                      <div class="card_center">
                        <div class="card_text">
                          <div class="card_tag">
                            <div class="card_title">
                              {{ element.name }}
                              <span v-if="showNew.includes(element.schemeId)" style="color: red">NEW</span>
                            </div>
                            <div>
                              <span class="cardTag" style="
                                  background-color: #d7e6ff;
                                  color: #2e7fff;
                                ">{{ element.category }}</span>
                              <span class="author">联系人：{{ element.contact }}</span>
                              <span>时间：{{
                                getYearMonthDay(element.createTime)
                              }}</span>
                            </div>
                          </div>
                          <div class="flex" style="align-items: center">
                            <div class="suggest" style="margin-right: 24px" v-if="
                              showNew.includes(element.schemeId) &&
                              element.classify != 0
                            " @click.stop="showSuggest">
                              <img style="width: 152px; height: 40px" src="@/assets/images/AI/suggest.png" alt="" />
                            </div>
                            <div class="viewStyle" v-if="
                              element.classify != 0 &&
                              isDraDisabled(element, 0)
                            " @click.stop="viewFile(element.fileList[0])">
                              <img class="add-icon" src=" @/assets/images/newProject/view.png" alt="" />
                              <span class="add"> &nbsp;预览</span>
                            </div>
                            <div class="cityStyle" v-if="
                              element.classify != 0 &&
                              isDraDisabled(element, 0)
                            " @click.stop="move(element)">
                              <img class="add-icon" src=" @/assets/images/AI/cancelAdd.png" alt="" />
                              <span class="add"> &nbsp;移出组合</span>
                            </div>
                            <div class="viewStyle" v-if="
                              element.classify == 0 &&
                              isDraDisabled(element, 0)
                            " @click.stop="viewFile(element.fileList[0])">
                              <img class="add-icon" src=" @/assets/images/newProject/view.png" alt="" />
                              <span class="add"> &nbsp;预览</span>
                            </div>
                            <div class="cityStyle" v-if="
                              element.classify == 0 &&
                              isDraDisabled(element, 0)
                            " @click.stop="move(element)">
                              <img class="add-icon" alt="" src=" @/assets/images/AI/cancelAdd.png" />
                              <span class="add"> &nbsp;移出组合</span>
                            </div>
                          </div>
                        </div>
                        <div class="card_des">
                          {{ element.introduce }}
                        </div>
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          " v-if="element.functionNames">
                          <div style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            ">
                            <span v-for="(
tagItem, tagIndex
                              ) in element.functionNames" :key="tagIndex" class="tag">
                              <span v-if="
                                tagIndex <
                                countTagIndex(element.functionNames)
                              ">
                                {{ tagItem }}
                              </span>
                            </span>
                            <span v-if="
                              sumTagLength(element.functionNames) &&
                              countTagIndex(element.functionNames) !=
                              element.functionNames.length
                            " class="tag" :title="element.functionNames
                              .slice(
                                countTagIndex(element.functionNames),
                                element.functionNames.length
                              )
                              .join(' , ')
                              ">
                              <span style="position: relative">
                                <div style="position: absolute; left: 4px">
                                  ...
                                </div>
                              </span>
                            </span>
                          </div>
                        </div>
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          ">
                          <div style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            ">
                            <span v-for="(fileItem, index) in element.fileList" :key="index" style="color: #0c70eb"
                              @click.stop="viewFile(fileItem)">
                              <!--  
  
                                <PaperClipOutlined />
                                <span style="margin-left: 4px">{{fileItem.fileName}}</span>
                                -->
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                    <div class="con_suggest" v-if="showNew.includes(element.schemeId) && isshowSuggest">
                      <div class="line1"></div>
                      <div class="top_box">
                        <div class="left">
                          <span class="key">麒麟AI助手智能推荐</span>
                          <span>可选择以下能力替换“{{ titleName }}”</span>
                        </div>
                        <div class="right">
                          <span @click.stop="changeData" class="action" style="margin-right: 36px" v-if="!isLess">
                            <img src="@/assets/images/AI/huan.png" alt="" />
                            <span>换一批</span>
                          </span>
                          <span class="action" @click.stop="closeSuggest">
                            <img src="@/assets/images/AI/delete.png" alt="" />
                            <span>关闭推荐</span>
                          </span>
                        </div>
                      </div>
                      <div class="cardList">
                        <div class="card" v-for="(item, index) in issuggestList" :key="idnex"
                          @click.stop="aiDetail(item)">
                          <!--<img :src="`${item.abilityPicture}`" alt="" />-->

                          <img v-if="item.abilityPicture" :src="`${item.abilityPicture}`" alt="" />
                          <div v-else style="
                              width: 112px;
                              height: 70px;
                              text-align: center;
                              position: relative;
                              margin-right: 8px;
                            " :style="backgroundStyles()">
                            <p style="
                                font-weight: 700;
                                display: block;
                                color: #1f82c8;
                                position: absolute;
                                left: 50%;
                                top: 50%;
                                font-size: 8px;
                                transform: translate(-50%, -50%);
                              ">
                              {{ item.name }}
                            </p>
                          </div>

                          <div class="card_right">
                            <div class="name">{{ item.name }}</div>
                            <span class="desc">{{ item.abilityIntro }}</span>
                            <div class="action" @click.stop="replaceAbility(item, element)">
                              <img src="@/assets/images/AI/tab.png" alt="" />
                              <span>替换能力</span>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
            <div class="btncontent">
              <div class="btn" @click="goChose(5, '应用场景')">
                <PlusCircleOutlined /> 点击添加应用场景
              </div>
            </div>
          </div>
        </div>
        <div v-else>
          <div class="btncontent">
            <div class="btn" @click="goChose(5, '应用场景')" style="margin-bottom: 24px">
              <PlusCircleOutlined /> 点击添加应用场景
            </div>
          </div>
        </div>
      </div>

      <div class="listContent scrollable-content">
        <div class="flex just-sb line" ref="btnContent">
          <div class="title">
            应用案例
            <span class="sub-count">({{
              AllcombineList.combineList8
                ? AllcombineList.combineList8.length
                : 0
            }})</span>
          </div>
        </div>
        <div class="tabContent" v-if="
          AllcombineList.combineList8 &&
          AllcombineList.combineList8.length > 0
        ">
          <div class="cardContent">
            <div class="card_total flex-1">
              <draggable :list="AllcombineList.combineList8" animation="300" filter=".forbid" :move="onMove">
                <template v-for="(element, index) in AllcombineList.combineList8" :key="index" #item="{ element }">
                  <div :class="isDraDisabled(element, 0)
                    ? ['card_content']
                    : ['card_content', 'forbid']
                    " @click="goDetail(element)">
                    <img v-if="element.new" style="width: 48px; height: 20px" class="newImg" alt=""
                      src="@/assets/images/combine/new.png" />
                    <div style="display: flex; padding: 24px">
                      <div class="imgbox">
                        <img style="width: 28px; height: 28px" class="moduleImg" alt=""
                          src="@/assets/images/combine/modelBefore.png" />
                        <a-image :width="168" :height="105" :preview="false" v-if="element.picture"
                          :src="`${element.picture.split(',')[0]}`" style="width: 100%; height: 100%" />
                        <img src="@/assets/images/home/<USER>" alt="" style="width: 168px; height: 105px" v-else />
                      </div>
                      <div class="card_center">
                        <div class="card_text">
                          <div class="card_tag">
                            <div class="card_title" v-if="element.projectName != null">
                              {{ element.projectName }}
                            </div>
                            <div class="card_title" v-else>
                              {{ element.name }}
                            </div>
                            <div>
                              <span class="cardTag" style="
                                  background-color: #d7e6ff;
                                  color: #2e7fff;
                                ">{{ element.category }}</span>
                              <span class="author">联系人：{{ element.contact }}</span>
                              <span>时间：{{
                                getYearMonthDay(element.createTime)
                              }}</span>
                            </div>
                          </div>
                          <div class="flex">
                            <div class="viewStyle" v-if="isDraDisabled(element, 0)"
                              @click.stop="viewFile(element.fileList[0])">
                              <img class="add-icon" src=" @/assets/images/newProject/view.png" alt="" />
                              <span class="add"> &nbsp;预览</span>
                            </div>
                            <div class="cityStyle" v-if="isDraDisabled(element, 0)" @click.stop="move(element)">
                              <img class="add-icon" src=" @/assets/images/AI/cancelAdd.png" alt="" />
                              <span class="add"> &nbsp;移出组合</span>
                            </div>
                          </div>
                        </div>
                        <div class="card_des" v-if="element.projectIntroduction != null">
                          {{ element.projectIntroduction }}
                        </div>
                        <div class="card_des" v-else>
                          {{ element.introduce }}
                        </div>
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          " v-if="element.functionNames">
                          <div style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            ">
                            <span v-for="(
tagItem, tagIndex
                              ) in element.functionNames" :key="tagIndex" class="tag">
                              <span v-if="
                                tagIndex <
                                countTagIndex(element.functionNames)
                              ">
                                {{ tagItem }}
                              </span>
                            </span>
                            <span v-if="
                              sumTagLength(element.functionNames) &&
                              countTagIndex(element.functionNames) !=
                              element.functionNames.length
                            " class="tag" :title="element.functionNames
                              .slice(
                                countTagIndex(element.functionNames),
                                element.functionNames.length
                              )
                              .join(' , ')
                              ">
                              <span style="position: relative">
                                <div style="position: absolute; left: 4px">
                                  ...
                                </div>
                              </span>
                            </span>
                          </div>
                        </div>
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          ">
                          <div style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            ">
                            <span v-for="(fileItem, index) in element.fileList" :key="index" style="color: #0c70eb"
                              @click.stop="viewFile(fileItem)">
                              <!--
                              <PaperClipOutlined />
                              <span style="margin-left: 4px">{{fileItem.fileName}}</span>
                              -->
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </draggable>
            </div>
            <div class="btncontent">
              <div class="btn" @click="goChose(8, '应用案例')">
                <PlusCircleOutlined /> 点击添加应用案例
              </div>
            </div>
          </div>
        </div>
        <div v-else class="btncontent">
          <div class="btncontent">
            <div class="btn" @click="goChose(8, '应用案例')" style="margin-bottom: 24px">
              <PlusCircleOutlined /> 点击添加应用案例
            </div>
          </div>
        </div>
      </div>
      <!-- <div class="listContent scrollable-content">
        <div class="flex just-sb line" ref="productContent">
          <div class="title">
            标准产品
            <span class="sub-count">({{
              AllcombineList.combineList9
                ? AllcombineList.combineList9.length
                : 0
            }})</span>
          </div>
        </div>
        <div class="tabContent" v-if="
          AllcombineList.combineList9 &&
          AllcombineList.combineList9.length > 0
        ">
          <div class="cardContent">
            <div class="card_total flex-1">
              <draggable :list="AllcombineList.combineList9" animation="300">
                <template v-for="(element, index) in AllcombineList.combineList9" :key="index" #item="{ element }">
                  <div :class="['card_content']" @click="goDetail(element)">
                    <img v-if="element.new" style="width: 48px; height: 20px" class="newImg"
                      src="@/assets/images/combine/new.png" />
                    <div style="display: flex; padding: 24px">
                      <div class="imgbox">
                        <img style="width: 28px; height: 28px" class="moduleImg"
                          src="@/assets/images/combine/modelBefore.png" />
                        <a-image :width="168" :height="105" :preview="false" v-if="element.picture"
                          :src="`${element.picture}`" style="width: 100%;height: 100%;" />
                        <img src="@/assets/images/home/<USER>" style="width: 168px; height: 105px" v-else />
                      </div>
                      <div class="card_center">
                        <div class="card_text">
                          <div class="card_tag">
                            <div class="card_title" v-if="element.projectName != null">
                              {{ element.projectName }}
                            </div>
                            <div class="card_title" v-else>
                              {{ element.name }}
                              <span v-if="showNew.includes(element.schemeId)" style="color: red">NEW</span>
                            </div>
                            <div>
                              <span class="cardTag" style="
                                  background-color: #d7e6ff;
                                  color: #2e7fff;
                                ">{{ element.category }}</span>
                              <span class="author">联系人：{{ element.contact }}</span>
                              <span>时间：{{
                                getYearMonthDay(element.createTime)
                              }}</span>
                            </div>
                          </div>
                          <div class="flex">
                            <div class="viewStyle" v-if="element.fileList[0] && element.fileList[0].fileUrl"
                              @click.stop="viewFile(element.fileList[0])">
                              <img class="add-icon" src=" @/assets/images/newProject/view.png" />
                              <span class="add"> &nbsp;预览</span>
                            </div>
                            <div class="cityStyle" @click.stop="move(element)">
                              <img class="add-icon" src=" @/assets/images/AI/cancelAdd.png" />
                              <span class="add"> &nbsp;移出组合</span>
                            </div>
                          </div>
                        </div>
                        <div class="card_des" v-if="element.projectIntroduction != null">
                          {{ element.projectIntroduction }}
                        </div>
                        <div class="card_des" v-else>
                          {{ element.introduce }}
                        </div>
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          " v-if="element.functionNames">
                          <div style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            ">
                            <span v-for="(tagItem, index) in element.functionNames" :key="index" class="tag">
                              {{ tagItem }}
                            </span>
                          </div>
                        </div>
                        <div style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          ">
                          <div style="
                              display: flex;
                              align-items: center;
                              justify-content: space-between;
                            ">
                            <span v-for="(fileItem, index) in element.fileList" :key="index" style="color: #0c70eb"
                              @click.stop="viewFile(fileItem)">
                            </span>
                          </div>
                        </div>
                      </div>
                    </div>

                  </div>
                </template>
              </draggable>
            </div>
            <div class="btncontent">
              <div class="btn" @click="goChose(9, '应用案例')">
                <PlusCircleOutlined /> 点击添加标准产品
              </div>
            </div>
          </div>
        </div>
        <div v-else class="btncontent">
          <div class="btncontent">
            <div class="btn" @click="goChose(9, '应用案例')" style="margin-bottom: 24px">
              <PlusCircleOutlined /> 点击添加标准产品
            </div>
          </div>
        </div>
      </div> -->

      <div class="listContent scrollable-content">
        <div class="flex just-sb line">
          <div class="title">结束语<span class="sub-count"></span></div>
        </div>
        <a-form :model="formData" ref="formRef2" class="formRef2">
          <a-form-item label="结束语:" name="conclusion" :rules="[
            { required: true, message: '请输入结束语', trigger: 'blur' },
          ]">
            <a-textarea v-model:value="formData.conclusion" placeholder="请输入结束语" :rows="1" @blur="setTitle('end')" />
          </a-form-item>
        </a-form>
      </div>
      <div class="buttons flex just-sb">
        <div class="flex left">
          <a-button :class="{ isDisabled: isDisabled }" @click="clear">
            <span>清除</span>
          </a-button>
          <!-- <a-button :class="{ isDisabled: isDisabled }" @click="save('编辑组合名称')">
            <span>合并保存</span>
          </a-button> -->
        </div>
        <div class="flex right">
          <!-- zdl ↓ -->
          <!-- <a-button :class="{ fontDisabled: isDisabled }" @click="getExplain()">
            <span>生成讲解</span>
          </a-button> -->
          <!-- zdl ↑ -->
          <a-button :class="{ fontDisabled: isDisabled }" @click="view('选择预览文件')">
            <span>生成方案</span>
          </a-button>
          <!--<a-button :class="{ isDisabled: isDisabled }" @click="download">
            <span>合并下载</span>
          </a-button>-->
        </div>
      </div>
    </div>
  </div>
  <write-apply ref="applyModal" v-if="applyShow" :sourceIds="sourceIds" @submit-cancel="closeApplyModal" />

  <a-modal :visible="previewVisible" @cancel="closeModal" :title="title" :width="modalWidth" :destroyOnClose="true"
    :maskClosable="false" :footer="null">
    <templete #title>
      <img src="@/assets/images/combine/title.png" alt="" style="width: 14px; height: 8px" />
    </templete>
    <div v-if="type == '1'">
      <a v-if="fileUrlObj.wordFile" @click="viewFile(fileUrlObj.wordFile)">组合文件.doc</a>
      <br />
      <a v-if="fileUrlObj.pptFile" @click="viewFile(fileUrlObj.pptFile)">组合文件.ppt</a>
    </div>
    <div v-if="type == '2'">
      <a-form :model="formData" labelAlign="right" ref="groupForm">
        <a-row>
          <a-col :span="24">
            <a-form-item label="组合名称" name="name" :rules="[{ required: true, message: '请输入组合名称' }]">
              <a-input v-model:value="formData.name" placeholder="请输入组合名称">
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="flex just-center">
        <a-button style="margin-right: 20px" @click="closeModal">取消</a-button>
        <a-button type="primary" @click="submit()">
          <span>提交</span>
        </a-button>
      </div>
    </div>
    <div v-if="type == '3'">
      <chose-table :labelId="labelId" :nowCustomizeSolutionId="nowCustomizeSolutionId" :categoryId="categoryId"
        :choseType="choseType" :cover="formData.title" :conclusion="formData.conclusion" :showAbility="showAbility"
        @close="close"></chose-table>
    </div>
    <div v-if="type == '4'">
      <guide-table @close="closeGuide"></guide-table>
    </div>
    <div v-if="type == '6'">
      <choose-project @close="close" :cover="formData.title" :conclusion="formData.conclusion"></choose-project>
    </div>
  </a-modal>
  <a-modal :visible="manualOrAiVisible" @cancel="manualOrAiVisible = false" title="选择定制方法" width="634px"
    style="top: 30vh" :destroyOnClose="true" :maskClosable="false" :footer="null" :closable="false">
    <templete #header>
      <img src="@/assets/images/home/<USER>" alt="" style="width: 14px; height: 8px; margin-right: 10px" />
    </templete>
    <div class="flex just-sb" style="padding: 0 13px 13px">
      <div class="manual" @click="goGuide()" style="cursor: pointer">
        <img src="@/assets/images/newProject/manual.png" alt="" />
        <div>手动定制</div>
        <div>通过方案定制流程添加方案并选择场景</div>
      </div>
      <div class="ai" @click="AIPost()" style="cursor: pointer">
        <img src="@/assets/images/newProject/aiLogo.png" alt="" />
        <div>AI定制</div>
        <div>通过麒麟AI助手智能分析进行方案定制</div>
      </div>
    </div>
  </a-modal>
  <a-modal v-model:visible="showDownloadModal" title="" :closable="false" :mask-closable="false" :footer="null"
    :destroyOnClose="true" width="450px">
    <promptBox @downloadModalCancel="downloadModalCancel" @downloadModalConfirm="downloadModalConfirm"
      :msgObj="msgObj" />
  </a-modal>
  <a-modal v-model:visible="showDownloadForm" title="新增工单" :mask-closable="false" :footer="null" :destroyOnClose="true"
    width="600px">
    <reviewForm @downloadFormCancel="downloadFormCancel" @downloadFormConfirm="downloadFormConfirm" />
  </a-modal>
  <!-- zdl ↓ -->
  <a-modal v-model:visible="showExplainModal" :mask-closable="false" :footer="null" :closable="false"
    :destroyOnClose="true" width="600px">
    <template #title>
      <div style="text-align: center; width: 100%;font-size: 20px;">生成任务已创建</div>
    </template>
    <div class="margin_t_20" style="font-size: 20px;">
      <span>您的讲解生成任务已创建，小麟正在马不停蹄地为您生成，您可在</span>
      <span style="color: #0c70eb;font-weight: bold;">“我的生成 ->方案讲解”</span>
      <span>中查看生成进度哦！</span>
    </div>
    <div class="flex just-center margin_t_20">
      <div class="flex just-center">
        <a-checkbox v-model="checkedDownloadShow" style="font-size: 16px;">不再提示</a-checkbox>
      </div>
    </div>
    <div class="flex just-center margin_t_20">
      <a-button type="primary" @click="closeDownloadShow()">确认</a-button>
    </div>
  </a-modal>
  <!-- zdl ↑ -->
  <a-modal v-model:visible="showWorkbench" :mask-closable="false" :footer="null" :destroyOnClose="true" width="980px">
    <template #title>
      <div class="custom-title">
        <img src="@/assets//apply_icon.png" style="width: 17px;height: 8px;margin-right: 10px;" alt="工单图标"
          class="title-icon" />
        <span>调度清单确认</span>
      </div>
    </template>
    <workBench @closeModel='closeModel' :combineList=combineList></workBench>
  </a-modal>
</template>

<script>
import {
  defineComponent,
  defineEmits,
  reactive,
  toRefs,
  ref,
  onMounted,
  watch,
  computed,
} from "vue";
import draggable from "vuedraggable";
import writeApply from "./components/writeApply.vue";
import { useRouter, useRoute } from "vue-router";
import { VueDraggable } from "vue-draggable-plus";
import {
  myCombineList,
  myCombineAIList,
  mergeFile,
  combineSave,
  deleteCombine,
  isShowGuide,
  updateTitle,
  replace,
  addCombine,
} from "@/api/combine/combine.js";
import { pptTopdf } from "@/api/fileUpload/uploadFile.js";
import { PlusCircleOutlined, PaperClipOutlined } from "@ant-design/icons-vue";
import Recommend from "./components/recommendList.vue";
import ChoseTable from "./components/choseTable.vue";
import chooseProject from "./components/chooseProject.vue";
import superLoading from "@/components/superLoading/superLoading.vue";
import { message } from "ant-design-vue";
import eventBus from "@/utils/eventBus";
import GuideTable from "./components/guideTable.vue";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { useHomeStore } from "@/store";
import bac from "@/assets/images/noDataBac.png";
import { getNewDownCount } from "../../../api/solutionNew/detail";
import promptBox from "@/components/promptBox/index.vue";
import reviewForm from "@/components/reviewForm/index.vue";
import workBench from './components/workbench.vue'
export default defineComponent({
  components: {
    writeApply,
    Recommend,
    PlusCircleOutlined,
    workBench,
    ChoseTable,
    PaperClipOutlined,
    VueDraggable,
    draggable,
    superLoading,
    GuideTable,
    chooseProject,
    promptBox,
    reviewForm,
  },
  props: {
    isAIpush: {
      type: Boolean,
      default: false,
    },
    abilityListPush: {
      type: Array,
      default: [],
    },
    titleName: {
      type: String,
      default: "",
    },
    nowCustomizeSolutionId: {
      type: Number,
      default: null,
    },
  },
  emits: ["event"],
  setup(props, { emit }) {
    const Router = useRouter();
    const Route = useRoute();
    const groupForm = ref();
    const applyModal = ref();
    const container = ref(null);
    const btnContent = ref(null);
    const productContent = ref(null);
    const useStroe = useHomeStore();
    const moduleRefs = ref([]);
    const topContainer = ref(null); // 引用按钮容器，获取其高度
    const formRef1 = ref();
    const formRef2 = ref();

    const data = reactive({
      msgObj: '',
      showDownloadModal: false,
      showWorkbench: false,
      showDownloadForm: false,
      nowCustomizeSolutionId: props.nowCustomizeSolutionId,
      labelId: "",
      categoryId: "",
      backgroundImage: bac,
      manualOrAiVisible: false,
      showNew: [],
      isAIpush: props.isAIpush,
      activeKey: "1", //政策背景、市场分析、需求分析
      activeKeyList: [
        { index: "1", name: "政策背景", listName: "combineList1" },
        { index: "2", name: "市场分析", listName: "combineList2" },
        { index: "3", name: "需求分析", listName: "combineList3" },
      ],
      AIUrl: false,
      showAIIdList: [],
      endFunction: false,
      progressPercent: -1,
      previewUrl: "",
      title: "",
      type: "",
      pageN: 1,
      pageS: 3,
      isshowSuggest: false,
      isDisabled: undefined,
      loading: false,
      viewLoading: false,
      downLoading: false,
      applyShow: false,
      previewVisible: false,
      solutionList: {},
      moduleList: [],
      solutionCount: 0,
      moduleCount: 0,
      fileUrlObj: {},
      formData: {
        conclusion: "谢谢聆听",
        title: "",
      },
      showAbility: false,
      // 方案 能力ids对象
      ids: {},
      sourceIds: [],
      suggestList: [],
      showList: [],
      issuggestList: [],
      isLess: true,
      titleName: props.titleName,
      combineList: [
        {
          title: "政策背景",
          list: [],
        },
        {
          title: "市场分析",
          list: [],
        },
        {
          title: "需求分析",
          list: [],
        },
        {
          title: "方案概述",
          list: [],
        },

        {
          title: "应用场景",
          list: [],
        },
        {
          title: "部署方案",
          list: [],
        },
        {
          title: "合作模式",
          list: [],
        },
        {
          title: "应用案例",
          list: [],
        },
      ],
      Conclusion: "", //结束语
      modalWidth: "600px",
      choseType: null,
      isAll: null,
      selcetIndex: null,
      loadShow1: false,
      AllcombineList: {
        combineList1: [],
        combineList2: [],
        combineList3: [],
        combineList5: [],
        combineList8: [],
        combineList9: [],
      },
      combineName: "",
      showExplainModal: false,//zdl
      checkedDownloadShow: false,//zdl
    });

    const sumTagLength = computed(() => {
      return function (arr) {
        let str = 0;
        for (let i = 0; i < arr.length; i++) {
          if (str < 40) {
            str += arr[i].length;
          } else {
            break;
          }
        }
        // arr.forEach(item=>{
        //   str += item.length
        // })
        // console.log('str',str);
        return str > 40 ? true : false;
      };
    });

    const countTagIndex = computed(() => {
      return function (arr) {
        let str = 0;
        let index = 0;
        for (let i = 0; i < arr.length; i++) {
          if (str < 40) {
            str += arr[i].length;
            index = i;
          } else {
            index = i;
            break;
          }
        }
        console.log("index", index);

        return index == arr.length - 1 ? arr.length : index;
      };
    });

    watch(
      () => props.isAIpush,
      (newV) => {
        data.isAIpush = newV;
        if (newV) {
          getList();
          showGuide();
        }
      }
    );

    watch(
      () => props.titleName,
      (newV) => {
        if (newV) {
          data.titleName = newV;
        }
      }
    );
    //ai推荐跳转详情
    const aiDetail = (item) => {
      console.log("item", item);
      if (item.classify == 1) {
        Router.push({
          query: {
            id: item.id,
          },
          name: "modulelNew",
        });
      } else if (item.classify == 2 || item.classify == 4) {
        Router.push({
          query: {
            id: item.id,
          },
          name: "applyNew",
        });
      } else if (item.classify == 3 || item.classify == 0) {
        Router.push({
          query: {
            id: item.id,
          },
          name: "productDetail",
        });
      } else {
        Router.push({
          query: {
            id: item.id,
          },
          name: "solveDetailNew",
        });
      }
    };

    const backgroundStyles = () => {
      return {
        backgroundImage: `url(${data.backgroundImage})`, // 使用模板字符串来插入变量
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
      };
    };

    const scrollToElement = () => {
      const topContainerHeight = container.value?.offsetHeight || 0; // 获取按钮容器的高度
      // 确保滚动的内容是在可滚动的容器中
      const scrollableContent = document.querySelector(".ant-layout-content");
      if (btnContent && scrollableContent) {
        const targetPosition =
          btnContent.value.offsetTop - topContainerHeight - 500; // 计算滚动的目标位置
        // 滚动可滚动容器内的内容
        scrollableContent.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });
      }
    };

    const scrollToProductElement = () => {
      const topContainerHeight = container.value?.offsetHeight || 0; // 获取按钮容器的高度
      // 确保滚动的内容是在可滚动的容器中
      const scrollableContent = document.querySelector(".ant-layout-content");
      if (productContent && scrollableContent) {
        const targetPosition =
          productContent.value.offsetTop - topContainerHeight - 200; // 计算滚动的目标位置
        // 滚动可滚动容器内的内容
        scrollableContent.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });
      }
    };
    const showNewType = (isShowList) => {
      console.log("isShowList", isShowList);
      if (localStorage.getItem("lastArr")) {
        let lastArr = JSON.parse(localStorage.getItem("lastArr"));
        console.log(
          "zzzzzzzzzzdl",
          lastArr.some((item) => item.classify === 3)
        );
        data.showNew = [];
        lastArr.forEach((item) => {
          data.showNew.push(item.schemeId);
        });
        localStorage.removeItem("lastArr");
        setTimeout(() => {
          if (lastArr.some((item) => item.classify == 3)) {
            scrollToProductElement();
          } else {
            scrollToElement();
          }
        }, 500);
        //if (lastArr.some(item => item.classify == 1)) {
        //推荐能力
        data.suggestList = props.abilityListPush;
        data.isshowSuggest = true;
        let newList = JSON.parse(JSON.stringify(data.suggestList));
        let index = isShowList ? isShowList : 0;
        newList.splice(index, 1);
        if (newList.length > 3) {
          data.showList = [...newList, ...newList, ...newList];
          console.log(" data.showList11111", data.showList);
          data.isLess = false;
        } else if (newList.length == 0) {
          data.isshowSuggest = false;
        } else {
          data.showList = newList;
          data.isLess = true;
        }
        data.issuggestList = data.showList.slice(0, 3);
        //}
        if (lastArr.some((item) => item.classify === 0)) {
          //推荐产品
          data.isshowSuggest = false;
        }
      }
    };

    const getList = async (isShowList) => {
    	let res = {};
    	let comeFrom = localStorage.getItem("comeFrom");
    	if(comeFrom == "true"){
    		res = await myCombineAIList();
    	} else {
    		res = await myCombineList();
    	}
        for (let i of res.data.list) {
          if (i.type) {
            if (i.type == 0) {
              localStorage.setItem("AInowCustomizeSolutionId", i.schemeId);
            }
          }
        }
        console.log("ressssss", res);
        //res.data.list.splice(3, 1);
        //res.data.list.splice(5, 1);
        //res.data.list.splice(4, 1);
        data.combineList = res.data.list;
        if (
          res.data.list[0] &&
          res.data.list[0].list[0] &&
          res.data.list[0].list[0].parentIndustryId
        ) {
          data.labelId = res.data.list[0].list[0].parentIndustryId;
        }
        if (
          res.data.list[0] &&
          res.data.list[0].list[0] &&
          res.data.list[0].list[0].categoryId
        ) {
          data.categoryId = res.data.list[0].list[0].categoryId;
        }
        console.log(data.categoryId);
        if (
        	res.data.cover || 
          (res.data.list[0] &&
          res.data.list[0].list[0] &&
          res.data.list[0].list[0].cover)
        ) {
          data.formData.title = res.data.cover || res.data.list[0].list[0].cover;
          useStroe.coverName = res.data.cover || res.data.list[0].list[0].cover;
        }
        if (
          res.data.list[0] &&
          res.data.list[0].list[0] &&
          res.data.list[0].list[0].conclusion
        ) {
          data.formData.conclusion = res.data.list[0].list[0].conclusion;
        } else {
          data.formData.conclusion = "谢谢聆听";
        }
        data.combineList.forEach((el) => {
          if (el.list) {
            el.list.forEach((item) => {
              if (item.label) {
                item.label = item.label.split(",");
              }
            });
            data.AllcombineList["combineList" + el.type] = el.list;
          }
        });
        console.log(data.AllcombineList);
        data.combineName = data.AllcombineList.combineList1.length > 0 && data.AllcombineList.combineList1[0].cover ?
          data.AllcombineList.combineList1[0].cover : "";
        console.log(data.combineName, `opkpokpokpkpokop`);
        data.combineList = [{ title: "封面", list: [] }, ...data.combineList];
        data.AllcombineList.combineList = [
          ...data.AllcombineList.combineList1,
          ...data.AllcombineList.combineList2,
          ...data.AllcombineList.combineList3,
        ];
        showNewType(isShowList);
    };
    eventBus.on("customRefresh", getList);

    const move = (item, type) => {
      deleteCombine(item.id)
        .then((res) => {
          getList();
        })
        .catch((error) => { });
    };

    const clear = () => {
      emit("event");
      getIds();
      data.formData.conclusion = "";
      data.formData.title = "";
      if (data.ids.length > 0) {
        deleteCombine(data.ids.join(","))
          .then((res) => {
            getList();
            setTimeout(() => {
              data.formData.conclusion = "";
              data.formData.title = "";
            }, 500);
            eventBus.emit("cartRefresh");
          })
          .catch((error) => { });
      }
    };

    // 过滤方案ids和能力ids;
    const getIds = () => {
      data.ids = [];
      data.sourceIds = [];
      data.showAIIdList = [];
      data.combineList.forEach((el) => {
        if (el.list) {
          el.list.forEach((item) => {
            data.ids.push(item.id);
            data.showAIIdList.push(item.schemeId);
            if (item.classify == 1 ) {
              data.sourceIds.push({
                type: 1,
                sourceId: item.schemeId,
              });
            } else if (item.classify == 2 || item.classify == 4) {
              data.sourceIds.push({
                type: 2,
                sourceId: item.schemeId,
              });
            } else if (item.classify == 3 || item.classify == 0) {
              data.sourceIds.push({
                type: 3,
                sourceId: item.schemeId,
              });
            } else if (item.classify == 6) {
              data.sourceIds.push({
                type: 4,
                sourceId: item.schemeId,
              });
            } else if (item.classify == 5 && item.type == 1) {
              data.sourceIds.push({
                type: 0,
                sourceId: item.schemeId,
              });
            }
          });
        }
      });
    };

    // 预览
    const view = (title) => {
      // 下载超限
      console.log("data.combineList", data.combineList);
      data.isDisabled = true;
      getNewDownCount({
        businessId: null,
        businessType: null,
      }).then((res) => {
        if (res.code == 200) {
          if (res.data) {
            formRef1.value.validate().then(() => {
              formRef2.value.validate().then(() => {
                //data.title = title;
                let requiredValid =
                  data.combineList[1].list.length > 0 &&
                  data.combineList[2].list.length > 0;
                if (false) {
                  message.error("必填能力不能为空");
                } else {
                  getIds();
                  data.combineList.forEach((item) => {
                    if (item.type === 0) {
                      data.sourceIds.push({
                        sourceId: item.schemeId,
                        type: 0,
                      });
                    }
                  });
                  data.progressPercent = 0;
                  let percentTime = setInterval(() => {
                    if (data.progressPercent == 99) {
                      data.progressPercent = 99;
                    } else {
                      data.progressPercent += 1;
                    }
                  }, 1000);
                  
                  combineSave({
                    sourceIdList: data.sourceIds,
                    title: data.formData.title,
                    conclusion: data.formData.conclusion,
                    name: data.combineName,
                  })
                    .then((res) => {
                    	data.isDisabled = false;
                      if (res.code == 200) {
                        data.loadShow1 = false;
                        let windowOrigin = window.location.origin;
                        let token = localStorage.getItem("token");
                        let newHref = res.data;
                        if (res.data.includes(windowOrigin)) {
                          newHref = "/portal" + res.data.split(windowOrigin)[1]
                        }
                        window.open(windowOrigin + newHref + "?token=" + token);
                        clearInterval(percentTime);
                        data.progressPercent = 100;
                        data.endFunction = true;
                        data.progressPercent = -1;
                        data.viewLoading = false;
                        return false;
                        const newpage = Router.resolve({
                          name: "lookPdf",
                          query: {
                            urlMsg: encodeURIComponent(res.data.pptFile),
                            urlName: "麒麟文件平台",
                          },
                        });
                      } else {
                        data.loadShow1 = false;
                        data.viewLoading = false;
                        clearInterval(percentTime);
                        data.progressPercent = -1;
                      }
                    })
                    .catch((error) => {
                      closeModal();
                    });
                  // mergeFile({
                  //   sourceIdList: data.sourceIds,
                  //   ...data.formData,
                  // }).then((res) => {
                  //   if (res.code == 200) {
                  //     clearInterval(percentTime);
                  //     data.progressPercent = 100;
                  //     data.endFunction = true;
                  //     data.progressPercent = -1;
                  //     data.viewLoading = false;
                  //     window.open(res.data.pptFile);
                  //     return false;
                  //     const newpage = Router.resolve({
                  //       name: "lookPdf",
                  //       query: {
                  //         urlMsg: encodeURIComponent(res.data.pptFile),
                  //         urlName: "麒麟文件平台",
                  //       },
                  //     });
                  //   } else {
                  //     clearInterval(percentTime);
                  //     data.progressPercent = -1;
                  //     data.viewLoading = false;
                  //   }
                  // });
                }
              });
            });
          } else {
            if (res.code == 200) {
              if (res.msg.includes('5')) {
                data.msgObj = {
                  applyTimes: 1,
                  msg: res.msg,
                  fullPath: Route.fullPath
                }
              } else if (res.msg.includes('10')) {
                data.msgObj = {
                  applyTimes: 1,
                  msg: res.msg,
                  fullPath: Route.fullPath
                }
              } else if (res.msg.includes('额外下载次数已满') && !res.msg.includes('今日不可再提交')) {
                data.msgObj = {
                  applyTimes: 2,
                  msg: res.msg,
                  fullPath: Route.fullPath
                }
              } else if (res.msg.includes('今日不可再提交')) {
                data.msgObj = {
                  applyTimes: 3,
                  msg: res.msg,
                  fullPath: Route.fullPath
                }
              } else { }
              data.showDownloadModal = true
            }
          }
        }
      });
    };
    // 预览文件
    const viewFile = (val) => {
      data.viewLoading = true;
      pptTopdf({
        filePath: val.filePath,
        fileUrl: val.fileUrl,
      }).then((res) => {
        data.viewLoading = false;
        if (res.code == 200) {
          let windowOrigin = window.location.origin;
          let token = localStorage.getItem("token");
          let newHref = res.data;
          if (res.data.includes(windowOrigin)) {
            newHref = "/portal" + res.data.split(windowOrigin)[1]
          }
          const newpage = Router.resolve({
            name: "lookPdf",
            query: {
              urlMsg: encodeURIComponent(
                windowOrigin + newHref + "?token=" + token
              ),
              urlName: val.name,
            },
          });
          window.open(newpage.href, "_blank");
        }
      });
    };

    const showAI = () => {
      getIds();
      if (data.showAIIdList.length == 0) {
        message.error("请选择方案或能力");
        return false;
      }
      data.type = 5;
      data.modalWidth = "1400px";
      data.title = "麒麟AI助手建议";
      data.previewVisible = true;
    };

    // 保存
    const save = (title) => {
      formRef1.value.validate().then(() => {
        formRef2.value.validate().then(() => {
          data.title = title;
          let requiredValid =
            data.combineList[1].list.length > 0 &&
            data.combineList[2].list.length > 0;
          if (false) {
            message.error("必填能力不能为空");
          } else {
            data.type = "2";
            data.modalWidth = "600px";
            data.previewVisible = true;
          }
        });
      });
    };

    const submit = (val) => {
      formRef1.value.validate().then(() => {
        formRef2.value.validate().then(() => {
          groupForm.value.validate().then(() => {
            let requiredValid =
              data.combineList[1].list.length > 0 &&
              data.combineList[2].list.length > 0;

            if (false) {
              message.error("必填能力不能为空");
            } else {
              getIds();
              data.loading = true;
              data.loadShow1 = true;
              (data.viewLoading = false), (data.downLoading = false);
              data.loading = false;
              closeModal();
              data.isDisabled = true;
              combineSave({
                sourceIdList: data.sourceIds,
                ...data.formData,
              })
                .then((res) => {
                	data.isDisabled = false;
                  if (res.code == 200) {
                    data.loadShow1 = false;
                    //合并保存后清空
                    clear();
                    message.success("保存成功");
                  } else {
                    data.loadShow1 = false;
                  }
                })
                .catch((error) => {
                  closeModal();
                });
            }
          });
        });
      });
    };

    const download = () => {
      getIds();
      data.title = title;
      if (false) {
        message.error("必填能力不能为空");
      } else {
        data.downLoading = true;
        data.viewLoading = false;
        mergeFile({
          sourceIdList: data.sourceIds,
          ...data.formData,
        }).then((res) => {
          data.downLoading = false;
          let windowOrigin = window.location.origin;
          let token = localStorage.getItem("token");
          let newHref = res.data.pptFile;
          if (res.data.pptFile.includes(windowOrigin)) {
            newHref = "/portal" + res.data.pptFile.split(windowOrigin)[1]
          }
          window.open(windowOrigin + newHref + "?token=" + token);
        });
      }
    };
    const toApply = () => {
      getIds();
      data.applyShow = true;
      applyModal.value.previewVisible = true;
    };
    const closeModal = () => {
      data.previewVisible = false;
      getList();
      data.type = "";
    };

    const closeApplyModal = () => {
      data.applyShow = false;
    };

    const toList = (name) => {
      Router.push({
        name: name,
      });
    };

    const deleteRecord = () => {
      myCombineList()
        .then((res) => { })
        .catch((error) => { });
    };
    // 设置 moduleRefs 时，确保其长度一致，并且在索引有效时进行赋值
    const setModuleRef = (el, index) => {
      if (el && index >= 0 && index < data.combineList.length) {
        moduleRefs.value[index] = el;
      }
    };
    const scrollUp = (index) => {
      const section = moduleRefs.value[index];
      const topContainerHeight = topContainer.value?.offsetHeight || 0; // 获取按钮容器的高度
      let mathHeight = index == 0 ? 94 : 84;
      data.selcetIndex = index;
      // 确保滚动的内容是在可滚动的容器中
      const scrollableContent = document.querySelector(".ant-layout-content");

      if (section && scrollableContent) {
        const targetPosition =
          section.offsetTop - topContainerHeight - mathHeight; // 计算滚动的目标位置

        // 滚动可滚动容器内的内容
        scrollableContent.scrollTo({
          top: targetPosition,
          behavior: "smooth",
        });
      }
    };
    // 确保moduleRefs的长度足够
    const adjustModuleRefsLength = () => {
      if (moduleRefs.value.length < data.combineList.length) {
        moduleRefs.value = new Array(data.combineList.length).fill(null);
      }
    };
    const goChose = (type, title) => {
      //if (data.AllcombineList.combineList.length == 0) {
      //  message.warning("请先定制方案");
      //  return;
      //}
      if (type == 9) {
        data.type = 6;
        data.modalWidth = "1200px";
        data.title = "添加产品";
        data.previewVisible = true;
        return false;
      }
      data.type = 3;
      data.modalWidth = "1200px";
      data.choseType = type;
      data.title = `添加${title}`;
      data.previewVisible = true;
      if (title == "应用场景") {
        data.showAbility = true;
      } else {
        data.showAbility = false;
      }
    };
    const goGuide = () => {
      data.type = 4;
      data.modalWidth = "1400px";
      data.choseType = 1;
      data.title = "选择方案";
      data.previewVisible = true;
      data.manualOrAiVisible = false;
    };
    // 在组件挂载时或者combineList更新时确保长度足够
    onMounted(() => {
      adjustModuleRefsLength();
      getList();
      showGuide();
    });

    watch(
      () => data.combineList,
      (newV) => {
        adjustModuleRefsLength();
      }
    );

    //是否显示新方案
    const showGuide = () => {
      isShowGuide().then((res) => {
        if (res.code == 200) {
          if (res.data) {
            // data.manualOrAiVisible = true;
            goGuide();
          } else {
            // data.manualOrAiVisible = false;
            AIPost()
          }
        }
      });
    };

    const isAll = computed(() => {
      let flag = true;

      data.combineList.forEach((el) => {
        if (
          (el.title == "市场分析" && el.list.length > 0) ||
          (el.title == "政策背景" && el.list.length > 0) ||
          (el.title == "需求分析" && el.list.length > 0)
        ) {
          flag = false;
        }
      });
      data.isAll = flag;
      return flag;
    });
    const isApplication = computed(() => {
      // isApplication//应用案例是否已填
      let flag = true;
      data.combineList.forEach((el) => {
        if (el.title == "应用场景" && el.list.length > 0) {
          flag = false;
        }
      });
      data.isAll = flag;
      return flag;
    });

    const loadShow = computed(() => {
      return data.viewLoading || data.downLoading || data.loading;
    });
    const getYearMonthDay = (datetimeStr) => {
      const datetime = new Date(datetimeStr);
      const year = datetime.getFullYear();
      // getMonth() 返回的月份是从0开始的，因此需要加1
      const month = datetime.getMonth() + 1;
      const day = datetime.getDate();

      // 将月份和日期转换为两位数字
      const monthStr = month < 10 ? "0" + month : month;
      const dayStr = day < 10 ? "0" + day : day;

      return `${year}-${monthStr}-${dayStr}`;
    };
    const goDetail = (item) => {
      console.log(item);
      if (item.type == 8) {
        Router.push({
          query: {
            id: item.schemeId,
          },
          name: "caseDetailNew",
        });
        return false;
      }
      if (item.classify == 1) {
        Router.push({
          query: {
            id: item.schemeId,
          },
          name: "modulelNew",
        });
      } else if (item.classify == 2 || item.classify == 4) {
        Router.push({
          query: {
            id: item.schemeId,
          },
          name: "applyNew",
        });
      } else if (item.classify == 3 || item.classify == 0) {
        Router.push({
          query: {
            id: item.schemeId,
          },
          name: "productDetail",
        });
      } else {
        Router.push({
          query: {
            id: item.schemeId,
          },
          name: "solveDetailNew",
        });
      }
    };
    const close = () => {
      data.previewVisible = false;
      setTimeout(() => {
        getList();
      }, 1000);
    };
    const closeGuide = () => {
      data.previewVisible = false;
      data.manualOrAiVisible = false;
      setTimeout(() => {
        getList();
      }, 1000);
    };
    const closeAI = () => {
      data.previewVisible = false;
      setTimeout(() => {
        getList();
      }, 1000);
    };
    const closeAImodel = () => {
      data.AIUrl = false;
      setTimeout(() => {
        getList();
      }, 1000);
    };
    const setTitle = (type) => {
      updateTitle({
        cover: data.formData.title,
        conclusion: data.formData.conclusion,
      })
        .then((res) => { })
        .catch((error) => { });
    };
    const AIPost = () => {
      // data.AIUrl = true;
      data.manualOrAiVisible = false;
    };

    //搜索框
    const seekContent = () => { };
    const showSuggest = () => {
      data.isshowSuggest = true;
    };

    const changeData = () => {
      data.pageN++;
      let num = data.pageN * data.pageS;
      data.issuggestList = data.showList.slice(data.pageN * 3 - 3, num);
      if (num >= data.showList.length) {
        data.pageN = 0;
      }
    };

    const replaceAbility = (item, element) => {
      let type = "";
      console.log(item);
      if (item.specialTitle == "能力") {
        type = 1;
      } else {
        type = 2;
      }
      let params = [
        {
          schemeId: item.id,
          classify: type,
          cover: data.formData.title,
          conclusion: data.formData.conclusion,
        },
      ];
      addCombine(params).then((res) => {
        if (res.code == 200) {
          localStorage.setItem("lastArr", JSON.stringify(params));
          deleteCombine(element.id).then(async (res) => {
            await getList(item.index);
            data.titleName = item.name;
            data.suggestList = props.abilityListPush;
            let newList = JSON.parse(JSON.stringify(data.suggestList));
            console.log(data.suggestList);
            console.log(item.index, newList);
            newList.splice(item.index, 1);
            if (newList.length > 3) {
              data.showList = [...newList, ...newList, ...newList];
              data.isLess = false;
            } else {
              data.showList = newList;
              data.isLess = true;
            }
            data.issuggestList = data.showList.slice(0, 3);
            data.isshowSuggest = false;
          });
        }
      });
    };
    const closeSuggest = () => {
      data.isshowSuggest = false;
    };

    const isDraDisabled = (item, index) => {
      if (item.fileList[index] && item.fileList[index].fileUrl != null) {
        return true;
      } else {
        return false;
      }
    };

    const onMove = (e) => {
      let item = e.relatedContext.element;
      if (item.fileList[0] && item.fileList[0].fileUrl != null) {
        return true;
      } else {
        return false;
      }
    };
    watch(
      () => props.nowCustomizeSolutionId,
      (newV) => {
        data.nowCustomizeSolutionId = newV;
      }
    );
    // 下载超限提示弹窗取消按钮
    const downloadModalCancel = () => {
      data.showDownloadModal = false;
    };
    // 下载超限提示弹窗确认按钮
    const downloadModalConfirm = () => {
      data.showDownloadModal = false;
      data.showDownloadForm = true;
    };
    const downloadFormCancel = () => {
      data.showDownloadForm = false;
    };
    const downloadFormConfirm = () => {
      data.showDownloadForm = false;
    };
    // zdl
    const getExplain = () => {
      console.log("生成讲解");
      data.showExplainModal = true
    };
    const closeDownloadShow = () => {
      message.success("任务创建成功");
      data.showExplainModal = false
    }
    // zdl ↑
    const showDetail = () => {
      console.log(data.combineList,`data.combineList`);
      
      if(data.combineList.find((item) => item.type === 1).list.length==0&&data.combineList.find((item) => item.type === 5).list.length==0){
        message.warning('请添加基础方案、应用场景和应用案例。')
      }else{
        data.showWorkbench = true
      }
    }
    const closeModel = () => {
      data.showWorkbench = false
    }
    return {
      ...toRefs(data),
      backgroundStyles,
      zhCN,
      showDetail,
      closeModel,
      applyModal,
      showSuggest,
      replaceAbility,
      closeSuggest,
      setTitle,
      deleteRecord,
      close,
      closeGuide,
      move,
      clear,
      loadShow,
      view,
      save,
      showAI,
      closeAI,
      download,
      closeModal,
      viewFile,
      groupForm,
      submit,
      toApply,
      toList,
      getList,
      scrollUp,
      moduleRefs,
      adjustModuleRefsLength,
      setModuleRef,
      closeApplyModal,
      topContainer,
      formRef1,
      formRef2,
      goChose,
      goGuide,
      isAll,
      getYearMonthDay,
      isApplication,
      goDetail,
      AIPost,
      closeAImodel,
      seekContent,
      container,
      btnContent,
      productContent,
      scrollToElement,
      scrollToProductElement,
      changeData,
      aiDetail,
      sumTagLength,
      countTagIndex,
      isDraDisabled,
      onMove,
      downloadModalCancel,
      downloadModalConfirm,
      downloadFormCancel,
      downloadFormConfirm,
      getExplain,
      closeDownloadShow
    };
  },
});
</script>

<style lang="scss" scoped src="./index.scss"></style>
<style lang="scss" scoped>
.combine {
  width: 1200px;
  margin: 0 auto 40px;
}

::v-deep(.ant-modal-header) {
  border-bottom: none !important;
}

// [ant-click-animating-without-extra-node='true']::after {
//   -antd-wave-shadow-color:none !important;
// }
.ant-btn.active {
  box-shadow: none;
}

.manual {
  display: flex;
  flex-flow: column;
  justify-content: space-around;
  align-items: center;
  padding: 10px;
  width: 260px;
  height: 180px;
  background: linear-gradient(135deg, #b1faff 0%, #a4cbff 100%);
  border-radius: 8px;
  opacity: 0.7;

  >img {
    width: 50px;
    height: 45px;
  }

  >div:nth-child(2) {
    font-weight: bold;
    font-size: 18px;
    color: #0176f5;
  }

  >div:last-child {
    font-weight: 400;
    font-size: 14px;
    color: #0176f5;
  }
}

.ai {
  display: flex;
  flex-flow: column;
  justify-content: space-around;
  align-items: center;
  padding: 10px;
  width: 260px;
  height: 180px;
  background: linear-gradient(135deg, #d2b1ff 0%, #a4cbff 100%);
  border-radius: 8px;
  opacity: 0.7;

  >img {
    width: 50px;
    height: 45px;
  }

  >div:nth-child(2) {
    font-weight: bold;
    font-size: 18px;
    color: #6b18ff;
  }

  >div:last-child {
    font-weight: 400;
    font-size: 14px;
    color: #6b18ff;
  }
}

.guide {
  background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
  border-radius: 4px 4px 4px 4px;
  color: #ffffff;
  padding: 9px 24px;
  cursor: pointer;
}

.isDisabled {
  background: rgba(12, 112, 235, 0.02);
  color: #fff !important;
  pointer-events: none !important;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  /* 半透明遮罩 */
  z-index: 9999;
}

.loading-super {
  position: fixed;
  top: 20vh;
  left: 20vw;
  width: 60vw;
  height: 60vh;
  z-index: 9999;
}

.AI-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.45);
  /* 半透明遮罩 */
  z-index: 9999;

  .AImodel {
    border-radius: 8px;
    width: calc(100vw - 352px);
    /*height: calc(100vh - 120px);*/
    position: relative;
    overflow: hidden;

    .title {
      width: 100%;
      height: 40px;
      line-height: 40px;
      text-align: center;
      font-size: 16px;
      background-color: #ffffff;
    }

    .close {
      position: absolute;
      top: 10px;
      right: 30px;
      cursor: pointer;
    }
  }
}

.fontDisabled {
  color: rgb(12, 112, 235, 0.3) !important;
  pointer-events: none !important;
}

.tag {
  span {
    padding: 0 8px;
    margin-right: 6px;
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    border: 1px solid rgba(0, 0, 0, 0.25);
    border-radius: 2px;
  }
}

.top {
  position: fixed;
  top: 180px;
  z-index: 999;
  width: 1200px;
  background-color: rgba(245, 247, 252);
}

.scrollable-content {
  overflow-y: auto;
  /* 使内容部分可以滚动 */
  // max-height: 500px;
  /* 根据需要调整滚动区域的高度 */
}

.btn_box {
  width: 100%;
  display: flex;
  background: #ffffff;
  border-bottom: 1px solid #f8fafd;
  color: rgba(0, 0, 0, 0.45);
  font-size: 16px;

  .btn {
    flex: 1;
    padding: 16px 20px;
    cursor: pointer;
    text-align: center;
  }

  .activeBtn {
    font-weight: 500;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
  }
}

.formRef1 {
  margin-top: 12px;

  :deep(.ant-input) {
    border: 1px solid #00000010;
  }

  :deep(.ant-input):focus {
    border: 1px solid #00000010;
    box-shadow: none;
  }

  :deep(.ant-input):hover {
    border: 1px solid #00000010;
  }
}

:deep(.ant-btn) {
  border: none !important;
}

.formRef2 {
  margin-top: 12px;

  :deep(.ant-input) {
    border: 1px solid #00000010;
  }

  :deep(.ant-input):focus {
    border: 1px solid #00000010;
    box-shadow: none;
  }

  :deep(.ant-input):hover {
    border: 1px solid #00000010;
  }
}

.isNot {
  color: #ff4d4f;
  margin-left: 80px;
}

.moduleImg {
  display: inline-block;
  margin-right: 24px;
}

.imgbox {
  display: flex;
  align-items: center;
}

:deep(.ant-tabs-bar) {
  margin: 0;

  .ant-tabs-tab {
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    font-weight: 500;
  }
}
</style>
