<template>
  <div class="textWord">
    附贴 <span class="num" v-if="speakList">{{ communt }}</span> 条
  </div>
  <div class="say_list area">
    <ul class="listBox">
      <li class="con" v-for="(item, index) in speakList" :key="index">
        <div class="con_left">
          <img src="@/assets/images/layout/person.png" alt="" />
        </div>
        <div class="con_right">
          <div class="con_title">
            <span class="name">
              {{ item.anonymity == 1 ? "匿名用户" : item.createName }}
            </span>
            <span class="circle margin_r_6" v-if="item.anonymity != 1">·</span>
            <span style="color: rgba(0, 0, 0, 0.65)">
              {{ item.anonymity == 1 ? "" : item.orgPathName }}</span
            >
            <span
              @click="deleteBtn(item.id)"
              style="color: red; margin-left: 16px; cursor: pointer"
              v-if="userInfo.id == item.createBy"
              >删除</span
            >
          </div>
          <div
            style="
              color: rgba(0, 0, 0, 0.65);
              width: 100%;
              word-wrap: break-word;
              white-space: pre-wrap
            "
            
          >
            {{ item.content }}
          </div>
          <div class="con_boot">
            <span class="time">{{ item.createTime }}</span>
            <span
              class="review pointer"
              v-if="item.replyShow"
              @click="replyBtn(item, index)"
            >
              回复
            </span>
          </div>
          <div class="replyContent" v-if="item.replyShow">
            <div class="backText">回复：</div>
            <a-textarea
              v-model:value="item.content"
              placeholder="请输入你想回复的内容"
              :rows="1"
            />
            <div class="flex just-end align-center margin_t_16">
              <a-button
                @click="cancelReplay(item, index)"
                type="primary"
                size="small"
                style="
                  background: rgba(1, 61, 253, 0.1);
                  border-radius: 4px;
                  font-weight: 500;
                  border: none;
                  color: #0c70eb;
                "
              >
                取消
              </a-button>
              <a-button
                type="primary"
                size="small"
                style="
                  background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
                  border-radius: 4px;
                  font-weight: 500;
                  border: none;
                  margin-left: 16px;
                "
              >
                发布
              </a-button>
            </div>
          </div>
        </div>
      </li>
    </ul>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, computed } from "vue";
import { setDeleteWord } from "@/api/community/index.js";
import { message } from "ant-design-vue";
export default defineComponent({
  props: {
    detailData: {
      type: Object,
      required: true,
    },
  },
  emits: ["refresh-page"],
  setup(props, { emit }) {
    const data = reactive({
      speakList: computed(() => props.detailData.commentList || []),
      communt: computed(() => props.detailData.commentList?.length || 0),
      userInfo: JSON.parse(localStorage.getItem("userInfo")),
    });
    
    const replyBtn = (_val, index) => {
      data.speakList[index].replyShow = true;
    };
    const cancelReplay = (_val, index) => {
      data.speakList[index].replyShow = false;
    };
    const deleteBtn = (v) => {
      setDeleteWord(v).then((res) => {
        if (res.code == 200) {
          message.success("删除成功");
          emit("refresh-page");
        }
      });
    };
    return {
      ...toRefs(data),
      replyBtn,
      deleteBtn,
      cancelReplay,
    };
  },
});
</script>
<style lang="scss" scoped>
::v-deep(.ant-input) {
  border: none;
}
.textWord {
  font-weight: bold;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
  margin-bottom: 12px;
  .num {
    font-weight: bold;
    font-size: 18px;
    color: #0c70eb;
  }
}
.say_list {
  .listBox {
    padding-left: 0;

    .con {
      display: flex;
      margin-bottom: 24px;
      width: 100%;

      .con_left {
        img {
          width: 40px;
          height: 40px;
        }
      }

      .con_right {
        margin-left: 16px;
        text-align: left;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 24px;
        flex: 1;

        .con_title {
          margin-bottom: 8px;

          .circle {
            font-size: 14px;
          }

          .name {
            font-weight: bold;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 22px;
            margin-right: 6px;
          }
        }

        .con_boot {
          display: flex;
          justify-content: space-between;

          .time {
            color: rgba(0, 0, 0, 0.65);
          }
          .review {
            color: #236cff;
          }
        }

        .replyContent {
          border: 1px solid #e8eaed;
          margin-top: 16px;
          padding: 12px 16px;
          border-radius: 4px;

          .backText {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.45);
          }
        }
      }
    }
  }
}
</style>