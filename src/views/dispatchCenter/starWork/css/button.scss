.custom_btn {
    color: #FFFFFF;
    border-radius: 4px 4px 4px 4px;
    border: none;
}

.reject_btn {
    background: linear-gradient(90deg, #FF845F 0%, #FF3730 100%);
    opacity: 1;
    cursor: pointer;

    &[disabled] {
        opacity: 0.6;
        cursor: not-allowed;
    }
}

.active_btn {
    background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
    opacity: 1;
    cursor: pointer;

    &[disabled] {
        opacity: 0.6;
        cursor: not-allowed;
    }
}

.cancel_btn {
    background: rgba(12, 112, 235, 0.08);
    color: #0C70EB;
}

// 只对非disabled状态的按钮应用hover效果
.custom_btn:not([disabled]):not(.is-disabled):not(.ant-btn-disabled):hover,
.custom_btn:not([disabled]):not(.is-disabled):not(.ant-btn-disabled):focus {
    opacity: 0.6;
}

// 确保disabled状态的按钮不会有hover效果
.custom_btn[disabled],
.custom_btn.is-disabled,
.custom_btn.ant-btn-disabled,
.custom_btn[disabled],
.custom_btn.is-disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    color: #FFFFFF !important;

    &:hover,
    &:focus {
        opacity: 0.6 !important;
        cursor: not-allowed !important;
        color: #FFFFFF !important;
    }
}

// Ant Design按钮disabled状态样式
:deep(.ant-btn.custom_btn[disabled]),
:deep(.ant-btn.custom_btn.ant-btn-disabled),
:deep(.ant-btn.custom_btn[disabled]:hover),
:deep(.ant-btn.custom_btn.ant-btn-disabled:hover),
:deep(.ant-btn.custom_btn[disabled]:focus),
:deep(.ant-btn.custom_btn.ant-btn-disabled:focus) {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    color: #FFFFFF !important;
}

// 针对reject_btn的特殊处理
.reject_btn.custom_btn[disabled],
.reject_btn.custom_btn.is-disabled,
.reject_btn.custom_btn.ant-btn-disabled,
.reject_btn.custom_btn[disabled],
.reject_btn.custom_btn.is-disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    color: #FFFFFF !important;
    background: linear-gradient(90deg, #FF845F 0%, #FF3730 100%) !important;

    &:hover,
    &:focus {
        opacity: 0.6 !important;
        cursor: not-allowed !important;
        color: #FFFFFF !important;
        background: linear-gradient(90deg, #FF845F 0%, #FF3730 100%) !important;
    }
}

// 针对active_btn的特殊处理（Ant Design）
.active_btn.custom_btn[disabled],
.active_btn.custom_btn.is-disabled,
.active_btn.custom_btn.ant-btn-disabled,
.active_btn.custom_btn[disabled],
.active_btn.custom_btn.is-disabled {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    color: #FFFFFF !important;
    background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%) !important;

    &:hover,
    &:focus {
        opacity: 0.6 !important;
        cursor: not-allowed !important;
        color: #FFFFFF !important;
        background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%) !important;
    }
}

// Ant Design active_btn disabled状态样式
:deep(.ant-btn.active_btn.custom_btn[disabled]),
:deep(.ant-btn.active_btn.custom_btn.ant-btn-disabled),
:deep(.ant-btn.active_btn.custom_btn[disabled]:hover),
:deep(.ant-btn.active_btn.custom_btn.ant-btn-disabled:hover),
:deep(.ant-btn.active_btn.custom_btn[disabled]:focus),
:deep(.ant-btn.active_btn.custom_btn.ant-btn-disabled:focus) {
    opacity: 0.6 !important;
    cursor: not-allowed !important;
    color: #FFFFFF !important;
    background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%) !important;
}