<template>
  <a-modal :visible="visible" title="处理意见" :footer="null" width="50%" @cancel="handleClose">
    <a-textarea :rows="7" :showCount="true" :maxlength="500" placeholder="请输入处理意见，限制500个字符"
      v-model:value="localSuggest" />
    <div class="modal-footer">
      <a-button class="margin_r_10 custom_btn cancel_btn" @click="handleClose"> 取消 </a-button>
      <a-button class="custom_btn active_btn" @click="handleOk"> 确认 </a-button>
    </div>
  </a-modal>
</template>

<script>
import { defineComponent, ref, watch } from 'vue';

export default defineComponent({
  name: 'RejectReasonModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    suggest: {
      type: String,
      default: ''
    },
  },
  emits: ['update:visible', 'update:suggest', 'ok', 'close'],
  setup(props, { emit }) {
    const localSuggest = ref(props.suggest);

    watch(() => props.visible, (isVisible) => {
      if (isVisible) {
        localSuggest.value = props.suggest;
      }
    });

    const handleClose = () => {
      emit('update:visible', false);
      emit('close');
    };

    const handleOk = () => {
      emit('update:suggest', localSuggest.value);
      emit('ok', localSuggest.value);
    };

    return {
      localSuggest,
      handleClose,
      handleOk
    };
  }
});
</script>
<style lang="scss" scoped>
@import "../../css/button.scss";

.modal-footer {
  width: 100%;
  text-align: center;
  margin-top: 24px;
}
</style>